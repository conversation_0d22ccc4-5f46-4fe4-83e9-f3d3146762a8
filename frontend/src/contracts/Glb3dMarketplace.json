[{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "nftAddress", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "Glb3dMarketplace__AlreadyListed", "type": "error"}, {"inputs": [{"internalType": "address", "name": "nftAddress", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "Glb3dMarketplace__AlreadyOwn", "type": "error"}, {"inputs": [], "name": "Glb3dMarketplace__NotApprovedForMarketplace", "type": "error"}, {"inputs": [], "name": "Glb3dMarketplace__NotGlb3dNft", "type": "error"}, {"inputs": [{"internalType": "address", "name": "nftAddress", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "Glb3dMarketplace__NotListed", "type": "error"}, {"inputs": [], "name": "Glb3dMarketplace__NotOfferCreator", "type": "error"}, {"inputs": [], "name": "Glb3dMarketplace__NotOwner", "type": "error"}, {"inputs": [], "name": "Glb3dMarketplace__NoProceeds", "type": "error"}, {"inputs": [], "name": "Glb3dMarketplace__OfferExpired", "type": "error"}, {"inputs": [], "name": "Glb3dMarketplace__OfferNotFound", "type": "error"}, {"inputs": [], "name": "Glb3dMarketplace__OfferTooLow", "type": "error"}, {"inputs": [{"internalType": "address", "name": "nftAddress", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "price", "type": "uint256"}], "name": "Glb3dMarketplace__PriceNotMet", "type": "error"}, {"inputs": [], "name": "Glb3dMarketplace__PriceMustBeAboveZero", "type": "error"}, {"inputs": [], "name": "Glb3dMarketplace__RoyaltyPaymentFailed", "type": "error"}, {"inputs": [], "name": "Glb3dMarketplace__TransferFailed", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "nftAddress", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "isFeatured", "type": "bool"}], "name": "FeaturedStatusChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "buyer", "type": "address"}, {"indexed": true, "internalType": "address", "name": "nftAddress", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "price", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "creator", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "royaltyAmount", "type": "uint256"}], "name": "ItemBought", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "seller", "type": "address"}, {"indexed": true, "internalType": "address", "name": "nftAddress", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "ItemCanceled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "seller", "type": "address"}, {"indexed": true, "internalType": "address", "name": "nftAddress", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "price", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "previewUri", "type": "string"}, {"indexed": false, "internalType": "bool", "name": "is3dGlb", "type": "bool"}], "name": "ItemListed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "seller", "type": "address"}, {"indexed": true, "internalType": "address", "name": "buyer", "type": "address"}, {"indexed": true, "internalType": "address", "name": "nftAddress", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "offerPrice", "type": "uint256"}], "name": "OfferAccepted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "buyer", "type": "address"}, {"indexed": true, "internalType": "address", "name": "nftAddress", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "OfferCanceled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "buyer", "type": "address"}, {"indexed": true, "internalType": "address", "name": "nftAddress", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "offerPrice", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "expirationTime", "type": "uint256"}], "name": "OfferCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newFeeBps", "type": "uint256"}], "name": "PlatformFeeUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "creator", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "RoyaltyPaid", "type": "event"}, {"inputs": [{"internalType": "address", "name": "nftAddress", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "address", "name": "buyer", "type": "address"}], "name": "acceptOffer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "nftAddress", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "buyItem", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "nftAddress", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "cancelListing", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "nftAddress", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "cancelOffer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "nftAddress", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "offerDuration", "type": "uint256"}], "name": "createOffer", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "count", "type": "uint256"}], "name": "get3dGlbListings", "outputs": [{"components": [{"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "address", "name": "seller", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "address", "name": "nftAddress", "type": "address"}, {"internalType": "bool", "name": "is3dGlb", "type": "bool"}, {"internalType": "string", "name": "previewUri", "type": "string"}], "internalType": "struct Glb3dMarketplace.Listing[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "startIndex", "type": "uint256"}, {"internalType": "uint256", "name": "count", "type": "uint256"}, {"internalType": "bool", "name": "onlyGlb", "type": "bool"}, {"internalType": "bool", "name": "onlyFeatured", "type": "bool"}], "name": "getActiveListings", "outputs": [{"components": [{"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "address", "name": "seller", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "address", "name": "nftAddress", "type": "address"}, {"internalType": "bool", "name": "is3dGlb", "type": "bool"}, {"internalType": "string", "name": "previewUri", "type": "string"}], "internalType": "struct Glb3dMarketplace.Listing[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "count", "type": "uint256"}], "name": "getFeaturedListings", "outputs": [{"components": [{"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "address", "name": "seller", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "address", "name": "nftAddress", "type": "address"}, {"internalType": "bool", "name": "is3dGlb", "type": "bool"}, {"internalType": "string", "name": "previewUri", "type": "string"}], "internalType": "struct Glb3dMarketplace.Listing[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "nftAddress", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "getListing", "outputs": [{"components": [{"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "address", "name": "seller", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "address", "name": "nftAddress", "type": "address"}, {"internalType": "bool", "name": "is3dGlb", "type": "bool"}, {"internalType": "string", "name": "previewUri", "type": "string"}], "internalType": "struct Glb3dMarketplace.Listing", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "nftAddress", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "address", "name": "buyer", "type": "address"}], "name": "get<PERSON>ffer", "outputs": [{"components": [{"internalType": "address", "name": "buyer", "type": "address"}, {"internalType": "uint256", "name": "offerPrice", "type": "uint256"}, {"internalType": "uint256", "name": "expirationTime", "type": "uint256"}, {"internalType": "bool", "name": "isActive", "type": "bool"}], "internalType": "struct Glb3dMarketplace.Offer", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "nftAddress", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "getOffers", "outputs": [{"internalType": "address[]", "name": "offerCreators", "type": "address[]"}, {"internalType": "uint256[]", "name": "offerPrices", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "expirationTimes", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getProceeds", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "nftAddress", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "price", "type": "uint256"}], "name": "listItem", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "nftAddress", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "bool", "name": "isFeatured", "type": "bool"}], "name": "setFeaturedStatus", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "feeBps", "type": "uint256"}], "name": "setPlatformFee", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "withdrawProceeds", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]