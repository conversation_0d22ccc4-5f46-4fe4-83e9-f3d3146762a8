[{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "acceptOffer", "inputs": [{"name": "nftAddress", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}, {"name": "buyer", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "buyItem", "inputs": [{"name": "nftAddress", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "cancelListing", "inputs": [{"name": "nftAddress", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "cancelOffer", "inputs": [{"name": "nftAddress", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "createOffer", "inputs": [{"name": "nftAddress", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}, {"name": "offerDuration", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "get3dGlbListings", "inputs": [{"name": "count", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "tuple[]", "internalType": "struct Glb3dMarketplace.Listing[]", "components": [{"name": "price", "type": "uint256", "internalType": "uint256"}, {"name": "seller", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}, {"name": "nftAddress", "type": "address", "internalType": "address"}, {"name": "is3dGlb", "type": "bool", "internalType": "bool"}, {"name": "previewUri", "type": "string", "internalType": "string"}]}], "stateMutability": "view"}, {"type": "function", "name": "getActiveListings", "inputs": [{"name": "startIndex", "type": "uint256", "internalType": "uint256"}, {"name": "count", "type": "uint256", "internalType": "uint256"}, {"name": "onlyGlb", "type": "bool", "internalType": "bool"}, {"name": "onlyFeatured", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "", "type": "tuple[]", "internalType": "struct Glb3dMarketplace.Listing[]", "components": [{"name": "price", "type": "uint256", "internalType": "uint256"}, {"name": "seller", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}, {"name": "nftAddress", "type": "address", "internalType": "address"}, {"name": "is3dGlb", "type": "bool", "internalType": "bool"}, {"name": "previewUri", "type": "string", "internalType": "string"}]}], "stateMutability": "view"}, {"type": "function", "name": "getFeaturedListings", "inputs": [{"name": "count", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "tuple[]", "internalType": "struct Glb3dMarketplace.Listing[]", "components": [{"name": "price", "type": "uint256", "internalType": "uint256"}, {"name": "seller", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}, {"name": "nftAddress", "type": "address", "internalType": "address"}, {"name": "is3dGlb", "type": "bool", "internalType": "bool"}, {"name": "previewUri", "type": "string", "internalType": "string"}]}], "stateMutability": "view"}, {"type": "function", "name": "getListing", "inputs": [{"name": "nftAddress", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct Glb3dMarketplace.Listing", "components": [{"name": "price", "type": "uint256", "internalType": "uint256"}, {"name": "seller", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}, {"name": "nftAddress", "type": "address", "internalType": "address"}, {"name": "is3dGlb", "type": "bool", "internalType": "bool"}, {"name": "previewUri", "type": "string", "internalType": "string"}]}], "stateMutability": "view"}, {"type": "function", "name": "getListingByNftAddress", "inputs": [{"name": "nftAddress", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct Glb3dMarketplace.Listing", "components": [{"name": "price", "type": "uint256", "internalType": "uint256"}, {"name": "seller", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}, {"name": "nftAddress", "type": "address", "internalType": "address"}, {"name": "is3dGlb", "type": "bool", "internalType": "bool"}, {"name": "previewUri", "type": "string", "internalType": "string"}]}], "stateMutability": "view"}, {"type": "function", "name": "get<PERSON>ffer", "inputs": [{"name": "nftAddress", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}, {"name": "buyer", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct Glb3dMarketplace.Offer", "components": [{"name": "buyer", "type": "address", "internalType": "address"}, {"name": "offerPrice", "type": "uint256", "internalType": "uint256"}, {"name": "expirationTime", "type": "uint256", "internalType": "uint256"}, {"name": "isActive", "type": "bool", "internalType": "bool"}]}], "stateMutability": "view"}, {"type": "function", "name": "getOffers", "inputs": [{"name": "nftAddress", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "offerCreators", "type": "address[]", "internalType": "address[]"}, {"name": "offerPrices", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "expirationTimes", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "getPlatformFeeBps", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getProceeds", "inputs": [{"name": "seller", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "isFeaturedListing", "inputs": [{"name": "nftAddress", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "listItem", "inputs": [{"name": "nftAddress", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}, {"name": "price", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setFeaturedListing", "inputs": [{"name": "nftAddress", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}, {"name": "isFeatured", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateListing", "inputs": [{"name": "nftAddress", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}, {"name": "newPrice", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updatePlatformFee", "inputs": [{"name": "newFeeBps", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "withdrawProceeds", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "FeaturedStatusChanged", "inputs": [{"name": "nftAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "isFeatured", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "ItemBought", "inputs": [{"name": "buyer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "nftAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "price", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "creator", "type": "address", "indexed": false, "internalType": "address"}, {"name": "royaltyAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "ItemCanceled", "inputs": [{"name": "seller", "type": "address", "indexed": true, "internalType": "address"}, {"name": "nftAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenId", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "ItemListed", "inputs": [{"name": "seller", "type": "address", "indexed": true, "internalType": "address"}, {"name": "nftAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "price", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "previewUri", "type": "string", "indexed": false, "internalType": "string"}, {"name": "is3dGlb", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "OfferAccepted", "inputs": [{"name": "seller", "type": "address", "indexed": true, "internalType": "address"}, {"name": "buyer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "nftAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenId", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "offerPrice", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "OfferCanceled", "inputs": [{"name": "buyer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "nftAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenId", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "OfferCreated", "inputs": [{"name": "buyer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "nftAddress", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "offerPrice", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "expirationTime", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "PlatformFeeUpdated", "inputs": [{"name": "newFeeBps", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "RoyaltyPaid", "inputs": [{"name": "creator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "Glb3dMarketplace__AlreadyListed", "inputs": [{"name": "nftAddress", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "Glb3dMarketplace__AlreadyOwn", "inputs": [{"name": "nftAddress", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}, {"name": "spender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "Glb3dMarketplace__NoProceeds", "inputs": []}, {"type": "error", "name": "Glb3dMarketplace__NotApprovedForMarketplace", "inputs": []}, {"type": "error", "name": "Glb3dMarketplace__NotGlb3dNft", "inputs": []}, {"type": "error", "name": "Glb3dMarketplace__NotListed", "inputs": [{"name": "nftAddress", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "Glb3dMarketplace__NotOfferCreator", "inputs": []}, {"type": "error", "name": "Glb3dMarketplace__NotOwner", "inputs": []}, {"type": "error", "name": "Glb3dMarketplace__OfferExpired", "inputs": []}, {"type": "error", "name": "Glb3dMarketplace__OfferNotFound", "inputs": []}, {"type": "error", "name": "Glb3dMarketplace__OfferTooLow", "inputs": []}, {"type": "error", "name": "Glb3dMarketplace__PriceMustBeAboveZero", "inputs": []}, {"type": "error", "name": "Glb3dMarketplace__PriceNotMet", "inputs": [{"name": "nftAddress", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}, {"name": "price", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "Glb3dMarketplace__RoyaltyPaymentFailed", "inputs": []}, {"type": "error", "name": "Glb3dMarketplace__TransferFailed", "inputs": []}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}]