[{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "approve", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "batchMintGlb3dNft", "inputs": [{"name": "glbUris", "type": "string[]", "internalType": "string[]"}, {"name": "previewUris", "type": "string[]", "internalType": "string[]"}, {"name": "names", "type": "string[]", "internalType": "string[]"}, {"name": "descriptions", "type": "string[]", "internalType": "string[]"}, {"name": "royaltyBpsArray", "type": "uint256[]", "internalType": "uint256[]"}], "outputs": [{"name": "", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "getApproved", "inputs": [{"name": "tokenId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "getCreator", "inputs": [{"name": "tokenId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "getDefaultRoyaltyBps", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getGlbMetadata", "inputs": [{"name": "tokenId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct Glb3dNft.GlbMetadata", "components": [{"name": "glbUri", "type": "string", "internalType": "string"}, {"name": "previewUri", "type": "string", "internalType": "string"}, {"name": "name", "type": "string", "internalType": "string"}, {"name": "description", "type": "string", "internalType": "string"}, {"name": "creator", "type": "address", "internalType": "address"}]}], "stateMutability": "view"}, {"type": "function", "name": "getRoyaltyBps", "inputs": [{"name": "tokenId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getTotalSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "isApprovedForAll", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "operator", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "mintGlb3dNft", "inputs": [{"name": "glbUri", "type": "string", "internalType": "string"}, {"name": "previewUri", "type": "string", "internalType": "string"}, {"name": "name", "type": "string", "internalType": "string"}, {"name": "description", "type": "string", "internalType": "string"}, {"name": "royaltyBps", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "ownerOf", "inputs": [{"name": "tokenId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "royaltyInfo", "inputs": [{"name": "tokenId", "type": "uint256", "internalType": "uint256"}, {"name": "salePrice", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "receiver", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "safeTransferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "safeTransferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setApprovalForAll", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "approved", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setDefaultRoyalty", "inputs": [{"name": "royaltyBps", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supportsInterface", "inputs": [{"name": "interfaceId", "type": "bytes4", "internalType": "bytes4"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "tokenURI", "inputs": [{"name": "tokenId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateGlbMetadata", "inputs": [{"name": "tokenId", "type": "uint256", "internalType": "uint256"}, {"name": "newGlbUri", "type": "string", "internalType": "string"}, {"name": "newPreviewUri", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateRoyalty", "inputs": [{"name": "tokenId", "type": "uint256", "internalType": "uint256"}, {"name": "royaltyBps", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "Approval", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "approved", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenId", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "ApprovalForAll", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "operator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "approved", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "BatchMetadataUpdate", "inputs": [{"name": "_fromTokenId", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "_toTokenId", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Glb3dNftMinted", "inputs": [{"name": "tokenId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "creator", "type": "address", "indexed": true, "internalType": "address"}, {"name": "glbUri", "type": "string", "indexed": false, "internalType": "string"}, {"name": "previewUri", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "MetadataUpdate", "inputs": [{"name": "_tokenId", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "MetadataUpdated", "inputs": [{"name": "tokenId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "glbUri", "type": "string", "indexed": false, "internalType": "string"}, {"name": "previewUri", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RoyaltyUpdated", "inputs": [{"name": "tokenId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "royaltyBps", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenId", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "ERC2981InvalidDefaultRoyalty", "inputs": [{"name": "numerator", "type": "uint256", "internalType": "uint256"}, {"name": "denominator", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC2981InvalidDefaultRoyaltyReceiver", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC2981InvalidTokenRoyalty", "inputs": [{"name": "tokenId", "type": "uint256", "internalType": "uint256"}, {"name": "numerator", "type": "uint256", "internalType": "uint256"}, {"name": "denominator", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC2981InvalidTokenRoyaltyReceiver", "inputs": [{"name": "tokenId", "type": "uint256", "internalType": "uint256"}, {"name": "receiver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC721IncorrectOwner", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}, {"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC721InsufficientApproval", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}, {"name": "tokenId", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC721InvalidApprover", "inputs": [{"name": "approver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC721InvalidOperator", "inputs": [{"name": "operator", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC721InvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC721InvalidReceiver", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC721InvalidSender", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC721NonexistentToken", "inputs": [{"name": "tokenId", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "Glb3dNft__InvalidGlbUri", "inputs": []}, {"type": "error", "name": "Glb3dNft__InvalidPreviewUri", "inputs": []}, {"type": "error", "name": "Glb3dNft__NotAuthorized", "inputs": []}, {"type": "error", "name": "Glb3dNft__TokenDoesNotExist", "inputs": []}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}]