interface ContractAddresses {
  [chainId: number]: {
    glb3dNft: string;
    glb3dMarketplace: string;
  };
}

// Contract addresses for different networks
export const contractAddresses: ContractAddresses = {
  // Anvil (local)
  31337: {
    glb3dNft: "******************************************",
    glb3dMarketplace: "******************************************",
  },
  // Sepolia testnet
  11155111: {
    glb3dNft: "******************************************", // Replace with actual address when deployed
    glb3dMarketplace: "******************************************", // Replace with actual address when deployed
  },
  // Ethereum mainnet
  1: {
    glb3dNft: "******************************************", // Replace with actual address when deployed
    glb3dMarketplace: "******************************************", // Replace with actual address when deployed
  },
};
