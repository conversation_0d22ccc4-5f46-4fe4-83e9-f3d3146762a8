import { useState, useEffect, useRef } from 'react';
import dynamic from 'next/dynamic';

// Dynamically import Three.js components to avoid SSR issues
const THREE = dynamic(() => import('three'), { ssr: false });
const GLTFLoader = dynamic(() =>
  import('three/examples/jsm/loaders/GLTFLoader').then(mod => mod.GLTFLoader),
  { ssr: false }
);
const OrbitControls = dynamic(() =>
  import('three/examples/jsm/controls/OrbitControls').then(mod => mod.OrbitControls),
  { ssr: false }
);

interface ModelScreenshotProps {
  file: File | null;
  onScreenshotTaken: (file: File) => void;
}

const ModelScreenshot: React.FC<ModelScreenshotProps> = ({ file, onScreenshotTaken }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const cameraRef = useRef<THREE.PerspectiveCamera | null>(null);

  useEffect(() => {
    if (!file || !containerRef.current) return;

    let controls: OrbitControls;
    let animationFrameId: number;

    const container = containerRef.current;

    const init = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Create scene
        const scene = new THREE.Scene();
        scene.background = new THREE.Color(0xf0f0f0);
        sceneRef.current = scene;

        // Add lights
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
        scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(1, 1, 1);
        scene.add(directionalLight);

        // Create camera
        const camera = new THREE.PerspectiveCamera(
          75,
          container.clientWidth / container.clientHeight,
          0.1,
          1000
        );
        camera.position.z = 5;
        cameraRef.current = camera;

        // Create renderer
        const renderer = new THREE.WebGLRenderer({ antialias: true, preserveDrawingBuffer: true });
        renderer.setSize(container.clientWidth, container.clientHeight);
        container.appendChild(renderer.domElement);
        rendererRef.current = renderer;

        // Add orbit controls
        controls = new OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;
        controls.dampingFactor = 0.25;
        controls.autoRotate = true;
        controls.autoRotateSpeed = 1;

        // Load the model from the file
        const objectUrl = URL.createObjectURL(file);
        const loader = new GLTFLoader();

        const gltf = await new Promise<THREE.GLTF>((resolve, reject) => {
          loader.load(
            objectUrl,
            (gltf) => resolve(gltf),
            undefined,
            (error) => reject(error)
          );
        });

        // Center the model
        const box = new THREE.Box3().setFromObject(gltf.scene);
        const center = box.getCenter(new THREE.Vector3());
        const size = box.getSize(new THREE.Vector3());

        // Reset model position to center
        gltf.scene.position.x = -center.x;
        gltf.scene.position.y = -center.y;
        gltf.scene.position.z = -center.z;

        // Adjust camera position based on model size
        const maxDim = Math.max(size.x, size.y, size.z);
        const fov = camera.fov * (Math.PI / 180);
        const cameraDistance = maxDim / (2 * Math.tan(fov / 2));

        camera.position.z = cameraDistance * 1.5;
        camera.updateProjectionMatrix();

        // Add the model to the scene
        scene.add(gltf.scene);

        // Clean up object URL
        URL.revokeObjectURL(objectUrl);

        // Animation loop
        const animate = () => {
          animationFrameId = requestAnimationFrame(animate);
          controls.update();
          renderer.render(scene, camera);
        };

        animate();
        setIsLoading(false);

        // Handle window resize
        const handleResize = () => {
          if (!container) return;

          camera.aspect = container.clientWidth / container.clientHeight;
          camera.updateProjectionMatrix();
          renderer.setSize(container.clientWidth, container.clientHeight);
        };

        window.addEventListener('resize', handleResize);

        // Clean up
        return () => {
          window.removeEventListener('resize', handleResize);
          cancelAnimationFrame(animationFrameId);
          if (renderer) {
            renderer.dispose();
          }
        };
      } catch (err) {
        console.error('Error loading 3D model:', err);
        setError('Failed to load 3D model. Please check the file format.');
        setIsLoading(false);
      }
    };

    init();

    // Clean up function
    return () => {
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
      if (rendererRef.current && containerRef.current) {
        containerRef.current.removeChild(rendererRef.current.domElement);
        rendererRef.current.dispose();
      }
    };
  }, [file]);

  const takeScreenshot = () => {
    if (!rendererRef.current || !sceneRef.current || !cameraRef.current) {
      setError('Cannot take screenshot: renderer not initialized');
      return;
    }

    try {
      // Render the scene
      rendererRef.current.render(sceneRef.current, cameraRef.current);

      // Get the canvas data URL
      const dataUrl = rendererRef.current.domElement.toDataURL('image/png');

      // Convert data URL to Blob
      const byteString = atob(dataUrl.split(',')[1]);
      const mimeString = dataUrl.split(',')[0].split(':')[1].split(';')[0];
      const ab = new ArrayBuffer(byteString.length);
      const ia = new Uint8Array(ab);

      for (let i = 0; i < byteString.length; i++) {
        ia[i] = byteString.charCodeAt(i);
      }

      const blob = new Blob([ab], { type: mimeString });

      // Create a File from the Blob
      const screenshotFile = new File([blob], `model-screenshot-${Date.now()}.png`, { type: 'image/png' });

      // Call the callback with the screenshot file
      onScreenshotTaken(screenshotFile);
    } catch (err) {
      console.error('Error taking screenshot:', err);
      setError('Failed to take screenshot');
    }
  };

  return (
    <div className="w-full">
      {isLoading && (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 p-4 rounded-md text-red-700 h-64 flex items-center justify-center">
          <p>{error}</p>
        </div>
      )}

      <div
        ref={containerRef}
        className={`w-full h-64 rounded-md ${!file ? 'bg-gray-100 flex items-center justify-center' : ''}`}
      >
        {!file && (
          <p className="text-gray-500 text-sm">3D model preview will appear here</p>
        )}
      </div>

      {file && !isLoading && !error && (
        <div className="mt-4 flex flex-col space-y-2">
          <button
            type="button"
            onClick={takeScreenshot}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm"
          >
            Capture as Preview Image
          </button>
          <p className="text-xs text-gray-500">
            The model is automatically rotating. Click the button when you like the angle.
          </p>
        </div>
      )}
    </div>
  );
};

export default ModelScreenshot;
