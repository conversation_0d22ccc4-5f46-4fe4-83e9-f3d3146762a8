'use client';

import { useState } from 'react';

interface SimpleModelPreviewProps {
  file: File | null;
}

const SimpleModelPreview: React.FC<SimpleModelPreviewProps> = ({ file }) => {
  const [error, setError] = useState<string | null>(null);

  return (
    <div className="w-full">
      {error && (
        <div className="bg-red-50 p-4 rounded-md text-red-700 h-64 flex items-center justify-center">
          <p>{error}</p>
        </div>
      )}
      
      <div 
        className={`w-full h-64 rounded-md bg-gray-100 flex items-center justify-center`}
      >
        {!file ? (
          <p className="text-gray-500 text-sm">3D model preview will appear here</p>
        ) : (
          <div className="flex flex-col items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 10l-2 1m0 0l-2-1m2 1v2.5M20 7l-2 1m2-1l-2-1m2 1v2.5M14 4l-2-1-2 1M4 7l2-1M4 7l2 1M4 7v2.5M12 21l-2-1m2 1l2-1m-2 1v-2.5M6 18l-2-1v-2.5M18 18l2-1v-2.5" />
            </svg>
            <p className="text-sm font-medium text-gray-700">{file.name}</p>
            <p className="text-xs text-gray-500 mt-1">{(file.size / 1024 / 1024).toFixed(2)} MB</p>
          </div>
        )}
      </div>
      
      {file && !error && (
        <div className="mt-2">
          <p className="text-xs text-gray-500">
            3D preview will be available when viewing the NFT
          </p>
        </div>
      )}
    </div>
  );
};

export default SimpleModelPreview;
