import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

// Dynamically import Three.js components to avoid SSR issues
const THREE = dynamic(() => import('three'), { ssr: false });
const GLTFLoader = dynamic(() =>
  import('three/examples/jsm/loaders/GLTFLoader').then(mod => mod.GLTFLoader),
  { ssr: false }
);
const OrbitControls = dynamic(() =>
  import('three/examples/jsm/controls/OrbitControls').then(mod => mod.OrbitControls),
  { ssr: false }
);

interface ModelPreviewProps {
  file: File | null;
}

const ModelPreview: React.FC<ModelPreviewProps> = ({ file }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!file) return;

    let scene: THREE.Scene;
    let camera: THREE.PerspectiveCamera;
    let renderer: THREE.WebGLRenderer;
    let controls: OrbitControls;
    let animationFrameId: number;

    const container = document.getElementById('model-preview-container');
    if (!container) return;

    // Clear previous content
    container.innerHTML = '';

    const init = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Create scene
        scene = new THREE.Scene();
        scene.background = new THREE.Color(0xf0f0f0);

        // Add lights
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
        scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(1, 1, 1);
        scene.add(directionalLight);

        // Create camera
        camera = new THREE.PerspectiveCamera(
          75,
          container.clientWidth / container.clientHeight,
          0.1,
          1000
        );
        camera.position.z = 5;

        // Create renderer
        renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(container.clientWidth, container.clientHeight);
        container.appendChild(renderer.domElement);

        // Add orbit controls
        controls = new OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;
        controls.dampingFactor = 0.25;

        // Load the model from the file
        const objectUrl = URL.createObjectURL(file);
        const loader = new GLTFLoader();

        const gltf = await new Promise<THREE.GLTF>((resolve, reject) => {
          loader.load(
            objectUrl,
            (gltf) => resolve(gltf),
            undefined,
            (error) => reject(error)
          );
        });

        // Center the model
        const box = new THREE.Box3().setFromObject(gltf.scene);
        const center = box.getCenter(new THREE.Vector3());
        const size = box.getSize(new THREE.Vector3());

        // Reset model position to center
        gltf.scene.position.x = -center.x;
        gltf.scene.position.y = -center.y;
        gltf.scene.position.z = -center.z;

        // Adjust camera position based on model size
        const maxDim = Math.max(size.x, size.y, size.z);
        const fov = camera.fov * (Math.PI / 180);
        const cameraDistance = maxDim / (2 * Math.tan(fov / 2));

        camera.position.z = cameraDistance * 1.5;
        camera.updateProjectionMatrix();

        // Add the model to the scene
        scene.add(gltf.scene);

        // Clean up object URL
        URL.revokeObjectURL(objectUrl);

        // Animation loop
        const animate = () => {
          animationFrameId = requestAnimationFrame(animate);
          controls.update();
          renderer.render(scene, camera);
        };

        animate();
        setIsLoading(false);

        // Handle window resize
        const handleResize = () => {
          if (!container) return;

          camera.aspect = container.clientWidth / container.clientHeight;
          camera.updateProjectionMatrix();
          renderer.setSize(container.clientWidth, container.clientHeight);
        };

        window.addEventListener('resize', handleResize);

        // Clean up
        return () => {
          window.removeEventListener('resize', handleResize);
          cancelAnimationFrame(animationFrameId);
          if (renderer) {
            renderer.dispose();
          }
        };
      } catch (err) {
        console.error('Error loading 3D model:', err);
        setError('Failed to load 3D model. Please check the file format.');
        setIsLoading(false);
      }
    };

    init();

    // Clean up function
    return () => {
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
    };
  }, [file]);

  return (
    <div className="w-full">
      {isLoading && (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 p-4 rounded-md text-red-700 h-64 flex items-center justify-center">
          <p>{error}</p>
        </div>
      )}

      <div
        id="model-preview-container"
        className={`w-full h-64 rounded-md ${!file ? 'bg-gray-100 flex items-center justify-center' : ''}`}
      >
        {!file && (
          <p className="text-gray-500 text-sm">3D model preview will appear here</p>
        )}
      </div>

      {file && !isLoading && !error && (
        <div className="mt-2">
          <p className="text-xs text-gray-500">
            Left click + drag: Rotate | Right click + drag: Pan | Scroll: Zoom
          </p>
        </div>
      )}
    </div>
  );
};

export default ModelPreview;
