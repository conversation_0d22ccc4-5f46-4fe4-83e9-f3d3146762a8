import axios from 'axios';

// Mock upload function that returns a fixed CID for testing
export const uploadFileToPinata = async (file: File, name?: string): Promise<string> => {
  try {
    console.log(`Uploading file: ${file.name}, size: ${file.size} bytes`);

    // For testing, return a fixed CID based on the file type
    if (file.type.includes('image')) {
      // Return the CID for the preview image
      return 'bafkreicdas32m2xygbt5jsbrsac5mkksgom25cfpl223imhhctz2aml7um';
    } else {
      // Return the CID for the GLB model
      return 'bafybeigkbibx7rlmvzjsism2x4sjt2ziblpk66wvi4hm343syraudwvcr4';
    }
  } catch (error) {
    console.error('Error in mock upload:', error);
    throw new Error('Failed to upload file to IPFS (mock)');
  }
};

/**
 * Get the IPFS URL for a CID
 * @param cid The IPFS CID
 * @returns The IPFS URL
 */
export const getIpfsUrl = (cid: string): string => {
  return `ipfs://${cid}`;
};

/**
 * Get the gateway URL for a CID (for displaying in browser)
 * @param cid The IPFS CID
 * @returns The gateway URL
 */
export const getGatewayUrl = (cid: string): string => {
  return `https://gateway.pinata.cloud/ipfs/${cid}`;
};
