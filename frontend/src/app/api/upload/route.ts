import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

// Pinata API configuration
const PINATA_API_KEY = process.env.NEXT_PUBLIC_PINATA_API_KEY;
const PINATA_SECRET_API_KEY = process.env.NEXT_PUBLIC_PINATA_SECRET_API_KEY;

export async function POST(request: NextRequest) {
  try {
    // Check if Pinata API keys are configured
    if (!PINATA_API_KEY || !PINATA_SECRET_API_KEY) {
      return NextResponse.json(
        { error: 'Pinata API keys are not configured' },
        { status: 500 }
      );
    }

    // Get the form data from the request
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const name = formData.get('name') as string;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Create a new FormData for the Pinata API
    const pinataFormData = new FormData();
    pinataFormData.append('file', file);

    // Add metadata if name is provided
    if (name) {
      const metadata = JSON.stringify({
        name: name || `Upload-${Date.now()}`,
      });
      pinataFormData.append('pinataMetadata', metadata);
    }

    // Upload to Pinata
    const response = await axios.post(
      'https://api.pinata.cloud/pinning/pinFileToIPFS',
      pinataFormData,
      {
        headers: {
          'pinata_api_key': PINATA_API_KEY,
          'pinata_secret_api_key': PINATA_SECRET_API_KEY,
        },
        maxContentLength: Infinity,
        maxBodyLength: Infinity,
      }
    );

    // Return the IPFS hash
    return NextResponse.json({
      success: true,
      ipfsHash: response.data.IpfsHash,
      ipfsUrl: `ipfs://${response.data.IpfsHash}`,
      gatewayUrl: `https://gateway.pinata.cloud/ipfs/${response.data.IpfsHash}`
    });
  } catch (error) {
    console.error('Error uploading to Pinata:', error);
    return NextResponse.json(
      { error: 'Failed to upload file to IPFS' },
      { status: 500 }
    );
  }
}
