'use client';

import { useState, useEffect } from 'react';
import { useAccount, useWriteContract, useWaitForTransactionReceipt, useChainId } from 'wagmi';
import Link from 'next/link';
import { contractAddresses } from '../../contracts/contractAddresses';
import Glb3dNftAbi from '../../contracts/Glb3dNft.json';
import FileUpload from '../../components/FileUpload';
import SimpleModelPreview from '../../components/SimpleModelPreview';
import { uploadFileToPinata, getIpfsUrl, getGatewayUrl } from '../../services/pinataService';

export default function MintPage() {
  const { address, isConnected } = useAccount();
  const [glbUri, setGlbUri] = useState('');
  const [previewUri, setPreviewUri] = useState('');
  const [glbFile, setGlbFile] = useState<File | null>(null);
  const [previewFile, setPreviewFile] = useState<File | null>(null);
  const [glbPreviewUrl, setGlbPreviewUrl] = useState<string | null>(null);
  const [previewImageUrl, setPreviewImageUrl] = useState<string | null>(null);
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [royaltyBps, setRoyaltyBps] = useState(1000); // Default 10%
  const [isLoading, setIsLoading] = useState(false);
  const [isUploadingGlb, setIsUploadingGlb] = useState(false);
  const [isUploadingPreview, setIsUploadingPreview] = useState(false);
  const [mintedTokenId, setMintedTokenId] = useState<number | null>(null);
  const [error, setError] = useState('');
  const [isSuccess, setIsSuccess] = useState(false);
  const [txHash, setTxHash] = useState<`0x${string}` | null>(null);

  // Get the current chain ID
  const chainId = useChainId();
  console.log('Current chain ID:', chainId);

  // Get the contract address for the current chain
  const nftAddress = contractAddresses[chainId]?.glb3dNft || contractAddresses[31337]?.glb3dNft; // Fallback to Anvil if chain not supported

  // Contract write function for minting an NFT
  const { writeContract } = useWriteContract();

  // Wait for transaction to be mined
  const { isLoading: isWaitingForTx, isSuccess: isTxSuccess, data: txReceipt } = useWaitForTransactionReceipt({
    hash: txHash || undefined,
  });

  // Handle transaction success
  useEffect(() => {
    if (isTxSuccess && txReceipt) {
      try {
        // Extract the token ID from the transaction receipt
        if (txReceipt.logs && txReceipt.logs.length > 0) {
          // The Glb3dNftMinted event has the token ID as the first indexed parameter
          const tokenIdHex = txReceipt.logs[0].topics?.[1];
          if (tokenIdHex) {
            const tokenId = parseInt(tokenIdHex, 16);
            setMintedTokenId(tokenId);
          }
        }
        setIsSuccess(true);
        setIsLoading(false);
      } catch (err) {
        console.error('Error parsing token ID from logs:', err);
        setIsSuccess(true);
        setIsLoading(false);
      }
    }
  }, [isTxSuccess, txReceipt]);

  // Handle GLB file selection and upload to IPFS
  const handleGlbFileSelected = async (file: File) => {
    try {
      setGlbFile(file);
      setIsUploadingGlb(true);
      setError('');

      // Create a preview URL for the file
      setGlbPreviewUrl(URL.createObjectURL(file));

      // Upload the file to IPFS via Pinata
      const cid = await uploadFileToPinata(file, `${name || 'GLB Model'}-${Date.now()}`);

      // Set the IPFS URI
      const ipfsUri = getIpfsUrl(cid);
      setGlbUri(ipfsUri);

      console.log('GLB file uploaded to IPFS:', ipfsUri);
      setIsUploadingGlb(false);
    } catch (err) {
      console.error('Error uploading GLB file:', err);
      setError('Error uploading GLB file: ' + (err instanceof Error ? err.message : String(err)));
      setIsUploadingGlb(false);
    }
  };

  // Handle preview image file selection and upload to IPFS
  const handlePreviewFileSelected = async (file: File) => {
    try {
      setPreviewFile(file);
      setIsUploadingPreview(true);
      setError('');

      // Create a preview URL for the image
      setPreviewImageUrl(URL.createObjectURL(file));

      // Upload the file to IPFS via Pinata
      const cid = await uploadFileToPinata(file, `${name || 'Preview'}-${Date.now()}`);

      // Set the IPFS URI
      const ipfsUri = getIpfsUrl(cid);
      setPreviewUri(ipfsUri);

      console.log('Preview image uploaded to IPFS:', ipfsUri);
      setIsUploadingPreview(false);
    } catch (err) {
      console.error('Error uploading preview image:', err);
      setError('Error uploading preview image: ' + (err instanceof Error ? err.message : String(err)));
      setIsUploadingPreview(false);
    }
  };



  // Function to mint an NFT
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Mint button clicked');
    setError('');
    setIsLoading(true);

    if (!isConnected) {
      console.log('Wallet not connected');
      setError('Please connect your wallet first');
      setIsLoading(false);
      return;
    }

    console.log('Checking form data...');
    console.log('GLB File:', glbFile);
    console.log('Preview File:', previewFile);
    console.log('Name:', name);
    console.log('Description:', description);

    if (!glbFile || !previewFile || !name || !description) {
      console.log('Missing required fields');
      setError('Please upload all files and fill in all fields');
      setIsLoading(false);
      return;
    }

    console.log('All fields are filled, proceeding with mint...');

    try {
      console.log('Contract address:', nftAddress);
      console.log('Arguments:', {
        glbUri,
        previewUri,
        name,
        description,
        royaltyBps: BigInt(royaltyBps)
      });

      if (!nftAddress) {
        console.error('NFT contract address is undefined');
        setError('NFT contract address is undefined. Please make sure you are connected to the correct network.');
        setIsLoading(false);
        return;
      }

      // Call the mintGlb3dNft function on the contract
      console.log('Calling writeContract...');

      try {
        writeContract({
          address: nftAddress as `0x${string}`,
          abi: Glb3dNftAbi,
          functionName: 'mintGlb3dNft',
          args: [glbUri, previewUri, name, description, BigInt(royaltyBps)],
        }, {
          onSuccess(hash) {
            // Save the transaction hash
            console.log('Transaction hash received:', hash);
            setTxHash(hash);

            console.log('Minting transaction submitted:', {
              hash,
              name,
              description,
              glbUri,
              previewUri,
              royaltyBps,
              owner: address
            });
          },
          onError(error) {
            console.error('Error minting NFT:', error);
            setError('Error minting NFT: ' + error.message);
            setIsLoading(false);
          }
        });
      } catch (innerErr) {
        console.error('Error calling writeContract:', innerErr);
        setError('Error calling contract: ' + (innerErr instanceof Error ? innerErr.message : String(innerErr)));
        setIsLoading(false);
      }

      console.log('writeContract called, waiting for response...');
    } catch (err) {
      console.error('Error minting NFT:', err);
      setError('Error minting NFT: ' + (err instanceof Error ? err.message : String(err)));
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h1 className="text-3xl font-bold mb-6">Mint a 3D GLB NFT</h1>

      {!isConnected ? (
        <div className="text-center p-6 bg-gray-100 rounded-lg">
          <p className="mb-4">Please connect your wallet to mint NFTs</p>
          <Link href="/" className="text-blue-600 hover:text-blue-800">
            Go back to home
          </Link>
        </div>
      ) : isSuccess && mintedTokenId ? (
        <div className="text-center p-6 bg-green-50 rounded-lg">
          <h2 className="text-2xl font-bold text-green-600 mb-2">NFT Minted Successfully!</h2>
          <p className="mb-4">Your NFT has been minted with Token ID: {mintedTokenId}</p>
          <div className="flex justify-center space-x-4">
            <Link
              href={`/my-nfts`}
              className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            >
              View My NFTs
            </Link>
            <button
              onClick={() => {
                setGlbUri('');
                setPreviewUri('');
                setGlbFile(null);
                setPreviewFile(null);
                setGlbPreviewUrl(null);
                setPreviewImageUrl(null);
                setName('');
                setDescription('');
                setRoyaltyBps(1000);
                setMintedTokenId(null);
                setIsSuccess(false);
                setError('');
              }}
              className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded"
            >
              Mint Another
            </button>
          </div>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
          <FileUpload
            label="Upload GLB File"
            accept=".glb"
            onFileSelected={handleGlbFileSelected}
            previewUrl={glbPreviewUrl || undefined}
            isLoading={isUploadingGlb}
          />
          {glbUri && (
            <div className="mt-2 mb-4">
              <p className="text-sm text-gray-500">IPFS URI: {glbUri}</p>
            </div>
          )}

          {glbFile && (
            <div className="mt-4 mb-6">
              <h3 className="text-sm font-medium text-gray-700 mb-2">3D Model Preview</h3>
              <SimpleModelPreview file={glbFile} />
            </div>
          )}

          <FileUpload
            label="Upload Preview Image"
            accept="image/*"
            onFileSelected={handlePreviewFileSelected}
            previewUrl={previewImageUrl || undefined}
            isLoading={isUploadingPreview}
          />
          {previewUri && (
            <div className="mt-2 mb-4">
              <p className="text-sm text-gray-500">IPFS URI: {previewUri}</p>
            </div>
          )}

          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700">
              NFT Name
            </label>
            <input
              type="text"
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="My Awesome 3D Model"
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>

          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700">
              Description
            </label>
            <textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Describe your 3D model..."
              rows={3}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>

          <div>
            <label htmlFor="royaltyBps" className="block text-sm font-medium text-gray-700">
              Royalty Percentage (in basis points, 100 = 1%)
            </label>
            <input
              type="number"
              id="royaltyBps"
              value={royaltyBps}
              onChange={(e) => setRoyaltyBps(parseInt(e.target.value))}
              min="0"
              max="5000"
              step="100"
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
            <p className="mt-1 text-xs text-gray-500">
              Current royalty: {royaltyBps / 100}%
            </p>
          </div>

          {error && (
            <div className="p-3 bg-red-50 text-red-700 rounded-md">
              {error}
            </div>
          )}

          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isLoading}
              className={`px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                isLoading && 'opacity-50 cursor-not-allowed'
              }`}
            >
              {isLoading ? 'Minting...' : 'Mint NFT'}
            </button>
          </div>
        </form>
      )}
    </div>
  );
}
