{"transactions": [{"hash": "0x7935ff7649fe38df4d8310dada7ad137ba80d6045c2b646caabe735e1e5f1bd3", "transactionType": "CREATE", "contractName": "FunWithStorage", "contractAddress": "0xe7f1725E7734CE288F8367e1Bb143E90bb3F0512", "function": null, "arguments": null, "rpc": "http://127.0.0.1:8545", "transaction": {"type": "0x02", "from": "0xf39fd6e51aad88f6f4ce6ab8827279cfffb92266", "gas": "0x3fd62", "value": "0x0", "data": "0x60a060405234801561001057600080fd5b50601960009081556001805460ff19908116821782556002805480840190915560de7f405787fa12a823e0f2b7631cc41b3ba8828b3321ca811111fa75cd3aa3bb5ace9091015591805260036020527f3617319a054d772f909f7c479a2cebe5066e836a939412e32403c99029b92eff8054909216179055607b60805260805160a26100a36000396000505060a26000f3fe6080604052348015600f57600080fd5b506004361060285760003560e01c8063874f33a114602d575b600080fd5b60336035565b005b6000805460429060016046565b5050565b80820180821115606657634e487b7160e01b600052601160045260246000fd5b9291505056fea2646970667358221220dc05b69ca32bbf0b23ab2378ded66dffc0db016b13b8a4c0054713df1408642564736f6c63430008130033", "nonce": "0x1", "accessList": []}, "additionalContracts": [], "isFixedGasLimit": false}], "receipts": [{"transactionHash": "0x7935ff7649fe38df4d8310dada7ad137ba80d6045c2b646caabe735e1e5f1bd3", "transactionIndex": "0x0", "blockHash": "0x81396aac9de10fba3d978bad7bcc1a5d8bb78ef65c934e2201e4aa0934da4bae", "blockNumber": "0x2", "from": "0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266", "to": null, "cumulativeGasUsed": "0x311ae", "gasUsed": "0x311ae", "contractAddress": "0xe7f1725E7734CE288F8367e1Bb143E90bb3F0512", "logs": [], "status": "0x1", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "type": "0x2", "effectiveGasPrice": "0xe7116214"}], "libraries": [], "pending": [], "path": "/Users/<USER>/code/foundry-full-course/foundry-fund-me-f23/broadcast/DeployStorageFun.s.sol/31337/run-latest.json", "returns": {"0": {"internal_type": "contract FunWithStorage", "value": "0xe7f1725E7734CE288F8367e1Bb143E90bb3F0512"}}, "timestamp": 1681332460, "chain": 31337, "multi": false, "commit": "8503ade"}