name: transpile upgradeable

on:
  push:
    branches:
      - master
      - release-v*

jobs:
  transpile:
    environment: push-upgradeable
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          repository: OpenZeppelin/openzeppelin-contracts-upgradeable
          fetch-depth: 0
          token: ${{ secrets.GH_TOKEN_UPGRADEABLE }}
      - name: Fetch current non-upgradeable branch
        run: |
          git fetch "$REMOTE" master # Fetch default branch first for patch to apply cleanly
          git fetch "$REMOTE" "$REF"
          git checkout FETCH_HEAD
        env:
          REF: ${{ github.ref }}
          REMOTE: https://github.com/${{ github.repository }}.git
      - name: Set up environment
        uses: ./.github/actions/setup
      - run: bash scripts/git-user-config.sh
      - name: Transpile to upgradeable
        run: bash scripts/upgradeable/transpile-onto.sh ${{ github.ref_name }} origin/${{ github.ref_name }}
        env:
          SUBMODULE_REMOTE: https://github.com/${{ github.repository }}.git
      - run: git push origin ${{ github.ref_name }}
